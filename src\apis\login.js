import { request } from "@/utils/axios";

/** 发送手机验证码 */
export function sendSmsCodeApi(data) {
  return request({
    url: "account/sendSms",
    method: "post",
    data,
  });
}

/** 重置密码 */
export function resetPasswordApi(userId) {
  return request({
    url: `system/user/${userId}/reset-password`,
    method: "patch",
  });
}

/** 登录并返回 Token */
export function loginApi(data) {
  return request({
    url: "account/login",
    method: "post",
    data,
  });
}
